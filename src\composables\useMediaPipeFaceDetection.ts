import * as faceLandmarksDetection from '@tensorflow-models/face-landmarks-detection'
import '@tensorflow/tfjs-backend-webgl'
import { useFaceData } from './useFaceData'
import type { FacePoint, FacePointName } from '@/types/face'

/**
 * MediaPipe Face Mesh检测器组合式函数
 * 提供真实的AI面部关键点检测功能
 */
export function useMediaPipeFaceDetection() {
  const faceData = useFaceData()
  let detector: faceLandmarksDetection.FaceLandmarksDetector | null = null
  let isInitializing = false

  // MediaPipe关键点索引映射到项目所需的6个关键点
  const MEDIAPIPE_KEYPOINT_MAPPING: Record<FacePointName, number> = {
    leftEye: 468, // 左眼中心
    rightEye: 473, // 右眼中心
    leftNostril: 48, // 左鼻翼
    rightNostril: 278, // 右鼻翼
    leftMouth: 61, // 左嘴角
    rightMouth: 291 // 右嘴角
  }

  // 内唇轮廓关键点索引（MediaPipe Face Mesh的内唇区域）
  // 这些点定义了牙齿可见的内唇边界，按顺序连接形成闭合轮廓
  // 基于MediaPipe Face Mesh 478个关键点的完整内唇轮廓
  const LIP_KEYPOINTS = [
    // 完整的内唇轮廓，按顺序连接形成闭合轮廓
    // 从左嘴角开始，逆时针方向绕一圈

    // 下内唇轮廓（从左嘴角到右嘴角）
    78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308,

    // 上内唇轮廓（从右嘴角回到左嘴角）
    415, 310, 311, 312, 13, 82, 81, 80
  ]

  /**
   * 初始化MediaPipe检测器
   */
  async function initializeDetector(): Promise<boolean> {
    if (detector) return true
    if (isInitializing) {
      // 等待初始化完成
      while (isInitializing) {
        await new Promise((resolve) => setTimeout(resolve, 100))
      }
      return !!detector
    }

    try {
      isInitializing = true
      console.log('正在初始化MediaPipe Face Mesh检测器...')
      const model = faceLandmarksDetection.SupportedModels.MediaPipeFaceMesh
      const detectorConfig = {
        runtime: 'mediapipe' as const,
        solutionPath: 'https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh',
        refineLandmarks: true, // 启用更精确的关键点检测
        maxFaces: 1 // 只检测一张脸
      }

      detector = await faceLandmarksDetection.createDetector(model, detectorConfig)
      console.log('MediaPipe Face Mesh检测器初始化成功')
      return true
    } catch (error) {
      console.error('MediaPipe检测器初始化失败:', error)
      return false
    } finally {
      isInitializing = false
    }
  }

  /**
   * 从图片检测面部关键点
   * @param imageElement HTML图片元素
   * @returns 检测结果
   */
  async function detectFaceFromImage(imageElement: HTMLImageElement) {
    try {
      // 确保检测器已初始化
      const initialized = await initializeDetector()
      if (!initialized || !detector) {
        throw new Error('检测器初始化失败')
      }

      console.log('开始检测面部关键点...')
      const startTime = performance.now()

      // 执行面部检测
      const faces = await detector.estimateFaces(imageElement, {
        flipHorizontal: false
      })

      const endTime = performance.now()
      console.log(`面部检测完成，耗时: ${(endTime - startTime).toFixed(2)}ms`)

      if (faces.length === 0) {
        console.warn('未检测到面部')
        return null
      }

      if (faces.length > 1) {
        console.warn(`检测到${faces.length}张面部，使用第一张`)
      }

      // 处理检测结果
      const result = processFaceDetectionResult(faces[0])
      console.log('面部关键点处理完成:', result)

      return result
    } catch (error) {
      console.error('面部检测失败:', error)
      throw error
    }
  }

  /**
   * 处理MediaPipe检测结果，转换为项目所需的数据格式
   * @param face MediaPipe检测到的面部数据
   * @returns 转换后的面部数据
   */
  function processFaceDetectionResult(face: any) {
    const keypoints = face.keypoints

    // 提取项目所需的6个关键点
    const facePoints = extractRequiredFacePoints(keypoints)

    // 提取唇线点
    const lipPoints = extractLipPoints(keypoints)

    // 提取对齐点
    const alignPoints = extractAlignPoints(keypoints)

    // 计算置信度（如果可用）
    const confidence = face.score || 0.9

    return {
      points: facePoints,
      smileLipPoints: lipPoints, // 微笑照唇线点
      mouthLipPoints: lipPoints, // 开口照唇线点（每张图片独立检测）
      smileAlignPoints: alignPoints, // 微笑照对齐点
      mouthAlignPoints: alignPoints, // 开口照对齐点（每张图片独立检测）
      confidence,
      totalKeypoints: keypoints.length
    }
  }

  /**
   * 从MediaPipe的478个关键点中提取项目所需的6个关键点
   * @param keypoints MediaPipe检测到的所有关键点
   * @returns 项目所需的6个关键点
   */
  function extractRequiredFacePoints(keypoints: any[]): FacePoint[] {
    const facePoints: FacePoint[] = []

    Object.entries(MEDIAPIPE_KEYPOINT_MAPPING).forEach(([name, index]) => {
      if (index < keypoints.length) {
        const point = keypoints[index]
        facePoints.push({
          name: name as FacePointName,
          x: point.x,
          y: point.y
        })
      }
    })

    return facePoints
  }

  /**
   * 从MediaPipe关键点中提取唇线点
   * @param keypoints MediaPipe检测到的所有关键点
   * @returns 唇线点数组
   */
  function extractLipPoints(keypoints: any[]): { x: number; y: number }[] {
    const lipPoints: { x: number; y: number }[] = []

    LIP_KEYPOINTS.forEach((index) => {
      if (index < keypoints.length) {
        const point = keypoints[index]
        lipPoints.push({
          x: point.x,
          y: point.y
        })
      }
    })

    return lipPoints
  }

  /**
   * 从MediaPipe关键点中提取对齐点
   * 通过上下唇部的特定点位计算两个对齐点
   * @param keypoints MediaPipe检测到的所有关键点
   * @returns 对齐点数组（2个点）
   */
  function extractAlignPoints(keypoints: any[]): { x: number; y: number }[] {
    const alignPoints: { x: number; y: number }[] = []

    // 使用上下唇部的特定点位来计算对齐点
    // 上唇点位：82、312
    // 下唇点位：87、317
    const upperLipLeft = 82 // 上唇左侧点
    const upperLipRight = 312 // 上唇右侧点
    const lowerLipLeft = 87 // 下唇左侧点
    const lowerLipRight = 317 // 下唇右侧点

    // 检查所有需要的点位是否存在
    if (
      upperLipLeft < keypoints.length &&
      upperLipRight < keypoints.length &&
      lowerLipLeft < keypoints.length &&
      lowerLipRight < keypoints.length
    ) {
      // 获取四个关键点的坐标
      const upperLeft = keypoints[upperLipLeft]
      const upperRight = keypoints[upperLipRight]
      const lowerLeft = keypoints[lowerLipLeft]
      const lowerRight = keypoints[lowerLipRight]

      // 计算第一个对齐点：82和87连线的中点（左侧）
      const leftAlignPoint = {
        x: (upperLeft.x + lowerLeft.x) / 2,
        y: (upperLeft.y + lowerLeft.y) / 2
      }

      // 计算第二个对齐点：312和317连线的中点（右侧）
      const rightAlignPoint = {
        x: (upperRight.x + lowerRight.x) / 2,
        y: (upperRight.y + lowerRight.y) / 2
      }

      alignPoints.push(leftAlignPoint, rightAlignPoint)
    } else {
      console.warn('无法获取对齐点所需的唇部关键点，使用备用方案')

      // 备用方案：使用嘴角点
      const leftMouthCorner = 61 // 左嘴角
      const rightMouthCorner = 291 // 右嘴角

      const backupIndices = [leftMouthCorner, rightMouthCorner]
      backupIndices.forEach((index) => {
        if (index < keypoints.length) {
          const point = keypoints[index]
          alignPoints.push({
            x: point.x,
            y: point.y
          })
        }
      })
    }

    return alignPoints
  }

  /**
   * 销毁检测器，释放资源
   */
  function dispose() {
    if (detector) {
      detector.dispose()
      detector = null
      console.log('MediaPipe检测器已销毁')
    }
  }

  /**
   * 检查检测器是否已初始化
   */
  function isDetectorReady(): boolean {
    return !!detector && !isInitializing
  }

  return {
    // 检测方法
    initializeDetector,
    detectFaceFromImage,

    // 工具方法
    dispose,
    isDetectorReady,

    // 数据处理方法
    extractRequiredFacePoints,
    extractLipPoints,

    // 继承useFaceData的所有方法和属性
    ...faceData
  }
}
