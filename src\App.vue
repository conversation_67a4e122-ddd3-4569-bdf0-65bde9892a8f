<script setup lang="ts">
import CSSLoading from '@/components/common/CSSLoading.vue'
import { globalCSSLoading } from '@/composables/useCSSLoading'
</script>

<template>
  <router-view />

  <!-- 全局CSS Loading组件 -->
  <CSSLoading
    :visible="globalCSSLoading.isVisible.value"
    :text="globalCSSLoading.loadingText.value"
    :lock="globalCSSLoading.isLocked.value"
    :background="globalCSSLoading.backgroundColor.value"
  />
</template>

<style scoped>
/* 保持全局字体和背景色 */
:global(body) {
  font-family: Arial, sans-serif;
  background: #f7f7f7;
  margin: 0;
}
</style>
