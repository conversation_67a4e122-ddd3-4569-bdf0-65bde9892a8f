import { ref, nextTick } from 'vue'

interface LoadingOptions {
  text?: string
  lock?: boolean
  background?: string
}

interface LoadingInstance {
  close: () => void
}

/**
 * CSS Loading 组合式函数
 * 提供基于CSS动画的loading功能，不会被主线程阻塞
 */
export function useCSSLoading() {
  // 全局loading状态
  const isVisible = ref(false)
  const loadingText = ref('')
  const isLocked = ref(true)
  const backgroundColor = ref('rgba(255, 255, 255, 0.7)')

  /**
   * 显示loading
   * @param options 配置选项
   * @returns loading实例，包含close方法
   */
  function show(options: LoadingOptions = {}): LoadingInstance {
    // 设置配置
    loadingText.value = options.text || ''
    isLocked.value = options.lock !== false
    backgroundColor.value = options.background || 'rgba(255, 255, 255, 0.7)'

    // 显示loading
    isVisible.value = true

    // 返回实例
    return {
      close: () => {
        isVisible.value = false
      }
    }
  }

  /**
   * 隐藏loading
   */
  function hide() {
    isVisible.value = false
  }

  /**
   * 服务式调用（类似Element Plus的ElLoading.service）
   * @param options 配置选项
   * @returns loading实例
   */
  function service(options: LoadingOptions = {}): LoadingInstance {
    return show(options)
  }

  /**
   * 异步操作包装器
   * 自动显示和隐藏loading
   * @param asyncFn 异步函数
   * @param options loading配置
   * @returns 异步函数的结果
   */
  async function withLoading<T>(
    asyncFn: () => Promise<T>,
    options: LoadingOptions = {}
  ): Promise<T> {
    const loadingInstance = show(options)

    try {
      // 确保loading动画有机会渲染
      await nextTick()
      await new Promise((resolve) => setTimeout(resolve, 50))

      // 执行异步操作
      const result = await asyncFn()
      return result
    } finally {
      // 无论成功还是失败都关闭loading
      loadingInstance.close()
    }
  }

  return {
    // 状态
    isVisible,
    loadingText,
    isLocked,
    backgroundColor,

    // 基础方法
    show,
    hide,
    service,

    // 高级方法
    withLoading
  }
}

// 创建全局实例
export const globalCSSLoading = useCSSLoading()
