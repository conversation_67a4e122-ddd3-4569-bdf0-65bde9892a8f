<template>
  <div v-if="visible" class="css-loading-overlay" :class="{ 'css-loading-overlay--lock': lock }">
    <div class="css-loading-content">
      <!-- 旋转圆环动画 -->
      <div class="css-loading-spinner">
        <div class="css-loading-ring"></div>
        <div class="css-loading-ring"></div>
        <div class="css-loading-ring"></div>
        <div class="css-loading-ring"></div>
      </div>
      
      <!-- 加载文本 -->
      <div v-if="text" class="css-loading-text">{{ text }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  visible?: boolean
  text?: string
  lock?: boolean
  background?: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  text: '',
  lock: true,
  background: 'rgba(255, 255, 255, 0.7)'
})
</script>

<style scoped>
.css-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: v-bind('props.background');
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  transition: opacity 0.3s ease;
}

.css-loading-overlay--lock {
  pointer-events: auto;
}

.css-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.css-loading-spinner {
  position: relative;
  width: 40px;
  height: 40px;
}

.css-loading-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-top: 3px solid #409eff;
  border-radius: 50%;
  animation: css-loading-spin 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
}

.css-loading-ring:nth-child(1) {
  animation-delay: -0.45s;
}

.css-loading-ring:nth-child(2) {
  animation-delay: -0.3s;
}

.css-loading-ring:nth-child(3) {
  animation-delay: -0.15s;
}

.css-loading-ring:nth-child(4) {
  animation-delay: 0s;
}

@keyframes css-loading-spin {
  0% {
    transform: rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: rotate(180deg);
    opacity: 0.6;
  }
  100% {
    transform: rotate(360deg);
    opacity: 1;
  }
}

.css-loading-text {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  margin-top: 8px;
  /* 添加文本闪烁动画 */
  animation: css-loading-text-pulse 1.5s ease-in-out infinite;
}

@keyframes css-loading-text-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .css-loading-spinner {
    width: 32px;
    height: 32px;
  }
  
  .css-loading-text {
    font-size: 12px;
  }
}

/* 高性能动画优化 */
.css-loading-ring {
  will-change: transform, opacity;
  transform: translateZ(0); /* 启用硬件加速 */
}

.css-loading-text {
  will-change: opacity;
  transform: translateZ(0); /* 启用硬件加速 */
}
</style>
